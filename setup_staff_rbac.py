#!/usr/bin/env python
"""
Setup script for Staff RBAC system
Run this script to set up the complete RBAC system
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pc_hardware.settings.common')
django.setup()

from django.core.management import call_command
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group

User = get_user_model()

def main():
    print("🚀 Setting up Staff RBAC System...")
    print("=" * 50)
    
    # Step 1: Create migrations
    print("\n1. Creating migrations...")
    try:
        call_command('makemigrations', 'staff', verbosity=1)
        print("✅ Migrations created successfully")
    except Exception as e:
        print(f"❌ Error creating migrations: {e}")
        return
    
    # Step 2: Run migrations
    print("\n2. Running migrations...")
    try:
        call_command('migrate', verbosity=1)
        print("✅ Migrations applied successfully")
    except Exception as e:
        print(f"❌ Error running migrations: {e}")
        return
    
    # Step 3: Setup groups and permissions
    print("\n3. Setting up groups and permissions...")
    try:
        call_command('setup_staff_groups', verbosity=1)
        print("✅ Groups and permissions set up successfully")
    except Exception as e:
        print(f"❌ Error setting up groups: {e}")
        return
    
    # Step 4: Show summary
    print("\n4. System Summary:")
    print("-" * 30)
    
    # Count groups
    group_count = Group.objects.count()
    print(f"📊 Total Groups: {group_count}")
    
    # Count staff users
    staff_count = User.objects.filter(is_staff=True).count()
    print(f"👥 Staff Users: {staff_count}")
    
    # Count superusers
    super_count = User.objects.filter(is_superuser=True).count()
    print(f"🔑 Superusers: {super_count}")
    
    print("\n✅ Staff RBAC System setup completed!")
    print("\n📋 Next Steps:")
    print("1. Assign users to groups:")
    print("   python manage.py assign_staff_user <email> <group_name> --make-staff")
    print("\n2. Test the API endpoints:")
    print("   POST /api/staff/auth/login/")
    print("   GET  /api/staff/groups/")
    print("   GET  /api/staff/users/")
    print("\n3. Available groups:")
    
    groups = Group.objects.all().order_by('name')
    for group in groups:
        member_count = group.user_set.filter(is_staff=True).count()
        print(f"   • {group.name} ({member_count} members)")
    
    print(f"\n🔗 API Base URL: http://localhost:8000/api/staff/")
    print("📚 Documentation: Check the API collection in old-docs/")

if __name__ == '__main__':
    main()
