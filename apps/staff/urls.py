from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import GroupViewSet, UserViewSet, PermissionViewSet, AuditViewSet
from .auth_views import current_user, user_permissions, check_permission

router = DefaultRouter()
router.register(r'groups', GroupViewSet, basename='staff-groups')
router.register(r'users', UserViewSet, basename='staff-users')
router.register(r'permissions', PermissionViewSet, basename='staff-permissions')
router.register(r'audit', AuditViewSet, basename='staff-audit')

app_name = 'staff'

urlpatterns = [
    # Authorization endpoints (authentication handled by core app)
    path('auth/user/', current_user, name='current-user'),
    path('auth/permissions/', user_permissions, name='user-permissions'),
    path('auth/check-permission/', check_permission, name='check-permission'),

    # Include router URLs
    path('', include(router.urls)),
]
