from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import GroupViewSet, UserViewSet, PermissionViewSet, AuditViewSet
from .auth_views import (
    staff_login, staff_logout, current_user, refresh_token,
    user_permissions, check_permission
)

# Create router for viewsets
router = DefaultRouter()
router.register(r'groups', GroupViewSet, basename='staff-groups')
router.register(r'users', UserViewSet, basename='staff-users')
router.register(r'permissions', PermissionViewSet, basename='staff-permissions')
router.register(r'audit', AuditViewSet, basename='staff-audit')

app_name = 'staff'

urlpatterns = [
    # Authentication endpoints
    path('auth/login/', staff_login, name='staff-login'),
    path('auth/logout/', staff_logout, name='staff-logout'),
    path('auth/user/', current_user, name='current-user'),
    path('auth/refresh/', refresh_token, name='refresh-token'),
    path('auth/permissions/', user_permissions, name='user-permissions'),
    path('auth/check-permission/', check_permission, name='check-permission'),
    
    # Include router URLs
    path('', include(router.urls)),
]
