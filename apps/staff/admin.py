from django.contrib import admin
from .models import GroupMembership, PermissionAudit, APIAccessLog


@admin.register(GroupMembership)
class GroupMembershipAdmin(admin.ModelAdmin):
    list_display = ['user', 'group', 'assigned_by', 'assigned_at', 'is_active']
    list_filter = ['group', 'is_active', 'assigned_at']
    search_fields = ['user__email', 'group__name']
    readonly_fields = ['assigned_at']
    date_hierarchy = 'assigned_at'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'user', 'group', 'assigned_by'
        )


@admin.register(PermissionAudit)
class PermissionAuditAdmin(admin.ModelAdmin):
    list_display = [
        'action', 'performed_by', 'target_user', 'target_group',
        'ip_address', 'timestamp'
    ]
    list_filter = ['action', 'timestamp']
    search_fields = [
        'performed_by__email', 'target_user__email', 'target_group__name'
    ]
    readonly_fields = ['timestamp']
    date_hierarchy = 'timestamp'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'performed_by', 'target_user', 'target_group'
        )

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        # Only superusers can delete audit logs
        return request.user.is_superuser


@admin.register(APIAccessLog)
class APIAccessLogAdmin(admin.ModelAdmin):
    list_display = [
        'user', 'endpoint', 'method', 'status_code',
        'response_time', 'timestamp'
    ]
    list_filter = ['method', 'status_code', 'timestamp']
    search_fields = ['user__email', 'endpoint', 'ip_address']
    readonly_fields = ['timestamp']
    date_hierarchy = 'timestamp'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        # Only superusers can delete access logs
        return request.user.is_superuser
