from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from apps.products.models import Product, Category, Brand, ProductType
from apps.order.models import Order
from apps.customers.models import Customer
from apps.core.models import User


class Command(BaseCommand):
    help = 'Setup initial groups and permissions for staff RBAC system'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Reset all groups and permissions before creating new ones',
        )
    
    def handle(self, *args, **options):
        self.stdout.write('Setting up staff RBAC groups and permissions...')
        
        if options['reset']:
            self.stdout.write('Resetting existing groups...')
            Group.objects.all().delete()
        
        # Define group permissions mapping
        group_permissions = {
            'Product Management Executive (PME)': [
                # Product permissions
                'products.add_product', 'products.change_product', 
                'products.delete_product', 'products.view_product',
                # Category permissions
                'products.add_category', 'products.change_category', 
                'products.delete_category', 'products.view_category',
                # Brand permissions
                'products.add_brand', 'products.change_brand', 
                'products.delete_brand', 'products.view_brand',
                # Product type permissions
                'products.add_producttype', 'products.change_producttype', 
                'products.delete_producttype', 'products.view_producttype',
            ],
            'Product Management Group Member (PMGM)': [
                # Product permissions (no delete)
                'products.add_product', 'products.change_product', 'products.view_product',
                # Category permissions (limited)
                'products.change_category', 'products.view_category',
                # Read-only for brands and types
                'products.view_brand', 'products.view_producttype',
            ],
            'Product Catalog Viewer (PCV)': [
                # Read-only permissions
                'products.view_product', 'products.view_category', 
                'products.view_brand', 'products.view_producttype',
            ],
            'Order Management Executive (OME)': [
                # Full order permissions
                'order.add_order', 'order.change_order', 
                'order.delete_order', 'order.view_order',
                # Customer view for order management
                'customers.view_customer',
            ],
            'Order Management Group Member (OMGM)': [
                # Order permissions (no delete)
                'order.change_order', 'order.view_order',
                # Customer view for support
                'customers.view_customer',
            ],
            'Order Fulfillment Specialist (OFS)': [
                # Limited order permissions for fulfillment
                'order.change_order', 'order.view_order',
            ],
            'Customer Management Executive (CME)': [
                # Full customer permissions
                'customers.add_customer', 'customers.change_customer', 
                'customers.delete_customer', 'customers.view_customer',
                # Order view for customer support
                'order.view_order',
            ],
            'Customer Support Representative (CSR)': [
                # Customer support permissions
                'customers.change_customer', 'customers.view_customer',
                # Order view for support
                'order.view_order',
            ],
            'Customer Data Analyst (CDA)': [
                # Read-only for analysis
                'customers.view_customer', 'order.view_order',
            ],
            'Content Management Executive (CTME)': [
                # Product permissions for content management
                'products.change_product', 'products.view_product',
            ],
            'Content Moderator (CTM)': [
                # Read-only for content viewing
                'products.view_product',
            ],
            'Finance Manager (FM)': [
                # Financial data access
                'order.view_order', 'customers.view_customer',
            ],
            'Business Analyst (BA)': [
                # Read-only access for analysis
                'products.view_product', 'products.view_category', 'products.view_brand',
                'order.view_order', 'customers.view_customer',
            ],
        }
        
        # Create groups and assign permissions
        created_count = 0
        updated_count = 0
        
        for group_name, permission_codenames in group_permissions.items():
            group, created = Group.objects.get_or_create(name=group_name)
            
            if created:
                self.stdout.write(f'✓ Created group: {group_name}')
                created_count += 1
            else:
                self.stdout.write(f'→ Group already exists: {group_name}')
                updated_count += 1
            
            # Clear existing permissions
            group.permissions.clear()
            
            # Add permissions
            permissions_added = 0
            for codename in permission_codenames:
                try:
                    permission = Permission.objects.get(codename=codename)
                    group.permissions.add(permission)
                    permissions_added += 1
                except Permission.DoesNotExist:
                    self.stdout.write(
                        self.style.WARNING(f'  ⚠ Permission not found: {codename}')
                    )
            
            self.stdout.write(f'  → Assigned {permissions_added} permissions')
        
        self.stdout.write('')
        self.stdout.write(
            self.style.SUCCESS(
                f'✓ Successfully set up staff RBAC system!\n'
                f'  Created: {created_count} groups\n'
                f'  Updated: {updated_count} groups'
            )
        )
        
        # Display next steps
        self.stdout.write('')
        self.stdout.write(self.style.WARNING('Next steps:'))
        self.stdout.write('1. Run: python manage.py assign_staff_user <email> <group_name>')
        self.stdout.write('2. Test the API endpoints')
        self.stdout.write('3. Configure frontend permissions')
