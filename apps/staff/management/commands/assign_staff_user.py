from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group
from apps.staff.services import GroupService

User = get_user_model()


class Command(BaseCommand):
    help = 'Assign a user to a staff group'
    
    def add_arguments(self, parser):
        parser.add_argument('email', type=str, help='User email')
        parser.add_argument('group_name', type=str, help='Group name')
        parser.add_argument(
            '--notes', 
            type=str, 
            help='Optional notes about the assignment', 
            default=''
        )
        parser.add_argument(
            '--make-staff',
            action='store_true',
            help='Make user staff if they are not already',
        )
        parser.add_argument(
            '--list-groups',
            action='store_true',
            help='List available groups',
        )
    
    def handle(self, *args, **options):
        if options['list_groups']:
            self.list_available_groups()
            return
        
        email = options['email']
        group_name = options['group_name']
        notes = options['notes']
        make_staff = options['make_staff']
        
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'User with email {email} not found')
            )
            return
        
        try:
            group = Group.objects.get(name=group_name)
        except Group.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'Group "{group_name}" not found')
            )
            self.stdout.write('Available groups:')
            self.list_available_groups()
            return
        
        # Check if user is staff
        if not user.is_staff:
            if make_staff:
                user.is_staff = True
                user.save()
                self.stdout.write(
                    self.style.SUCCESS(f'Made {user.email} a staff user')
                )
            else:
                self.stdout.write(
                    self.style.ERROR(
                        f'User {user.email} is not a staff user. '
                        'Use --make-staff to make them staff.'
                    )
                )
                return
        
        # Check if user is already in the group
        if user.groups.filter(id=group.id).exists():
            self.stdout.write(
                self.style.WARNING(f'User {user.email} is already in group "{group.name}"')
            )
            return
        
        # Add user to group
        group_service = GroupService()
        membership, created = group_service.add_user_to_group(
            user=user,
            group=group,
            assigned_by=None,  # System assignment
            notes=notes or f'Assigned via management command'
        )
        
        if created:
            self.stdout.write(
                self.style.SUCCESS(
                    f'✓ Successfully assigned {user.email} to group "{group.name}"'
                )
            )
            
            # Show user's current groups
            user_groups = user.groups.values_list('name', flat=True)
            self.stdout.write(f'User is now in groups: {", ".join(user_groups)}')
            
            # Show user's permissions
            permissions = user.get_all_permissions()
            self.stdout.write(f'User now has {len(permissions)} permissions')
        else:
            self.stdout.write(
                self.style.WARNING(f'Assignment may have failed or already existed')
            )
    
    def list_available_groups(self):
        """List all available groups"""
        groups = Group.objects.all().order_by('name')
        
        if not groups.exists():
            self.stdout.write(
                self.style.WARNING('No groups found. Run: python manage.py setup_staff_groups')
            )
            return
        
        self.stdout.write('\nAvailable groups:')
        self.stdout.write('-' * 50)
        
        for group in groups:
            member_count = group.user_set.filter(is_staff=True).count()
            permission_count = group.permissions.count()
            
            self.stdout.write(
                f'• {group.name}\n'
                f'  Members: {member_count} | Permissions: {permission_count}'
            )
        
        self.stdout.write('-' * 50)
        self.stdout.write(f'Total: {groups.count()} groups\n')
