from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.contrib.auth import get_user_model
from .serializers import AuthUserSerializer
from .permissions import IsStaffUser
from .services import SecurityService

User = get_user_model()


# Note: Authentication (login/logout) is handled by core app
# This file only contains staff-specific authorization views

@api_view(['GET'])
@permission_classes([IsStaffUser])
def current_user(request):
    """
    Get current authenticated staff user information with groups and permissions
    Note: Authentication is handled by core app, this only provides staff-specific data
    """
    serializer = AuthUserSerializer(request.user)
    return Response({
        'success': True,
        'user': serializer.data
    })


@api_view(['GET'])
@permission_classes([IsStaffUser])
def user_permissions(request):
    """
    Get current user's permissions and groups
    """
    user = request.user

    # Get user groups
    groups = list(user.groups.values_list('name', flat=True))

    # Get all permissions
    permissions = list(user.get_all_permissions())

    # Get group permissions breakdown
    group_permissions = {}
    for group in user.groups.all():
        group_permissions[group.name] = list(
            group.permissions.values_list('codename', flat=True)
        )

    return Response({
        'success': True,
        'user_id': user.id,
        'email': user.email,
        'is_superuser': user.is_superuser,
        'groups': groups,
        'permissions': permissions,
        'group_permissions': group_permissions
    })


@api_view(['POST'])
@permission_classes([IsStaffUser])
def check_permission(request):
    """
    Check if the current user has specific permission
    """
    permission = request.data.get('permission')

    if not permission:
        return Response({
            'success': False,
            'error': 'Permission parameter is required'
        }, status=status.HTTP_400_BAD_REQUEST)

    has_permission = request.user.has_perm(permission)

    return Response({
        'success': True,
        'permission': permission,
        'has_permission': has_permission
    })
