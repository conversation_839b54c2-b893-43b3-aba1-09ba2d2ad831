from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import authenticate
from django.contrib.auth import get_user_model
from .serializers import AuthUserSerializer
from .permissions import IsStaffUser
from .services import AuditService, SecurityService

User = get_user_model()


@api_view(['POST'])
def staff_login(request):
    """
    Staff login endpoint
    Authenticates staff users and returns JWT tokens
    """
    email = request.data.get('email')
    password = request.data.get('password')
    
    if not email or not password:
        return Response({
            'success': False,
            'error': 'Email and password are required'
        }, status=status.HTTP_400_BAD_REQUEST)
    
    # Authenticate user
    user = authenticate(request, username=email, password=password)
    
    if not user:
        # Log failed login attempt
        security_service = SecurityService()
        AuditService.log_action(
            action='login_failed',
            performed_by=None,
            details={
                'email': email,
                'reason': 'invalid_credentials'
            },
            ip_address=security_service.get_client_ip(request)
        )
        
        return Response({
            'success': False,
            'error': 'Invalid credentials'
        }, status=status.HTTP_401_UNAUTHORIZED)
    
    if not user.is_staff:
        # Log unauthorized access attempt
        security_service = SecurityService()
        AuditService.log_action(
            action='unauthorized_access_attempt',
            performed_by=user,
            details={
                'reason': 'not_staff_user'
            },
            ip_address=security_service.get_client_ip(request)
        )
        
        return Response({
            'success': False,
            'error': 'Staff access required'
        }, status=status.HTTP_403_FORBIDDEN)
    
    if not user.is_active:
        return Response({
            'success': False,
            'error': 'Account is inactive'
        }, status=status.HTTP_403_FORBIDDEN)
    
    # Generate tokens
    refresh = RefreshToken.for_user(user)
    access_token = refresh.access_token
    
    # Serialize user data
    user_serializer = AuthUserSerializer(user)
    
    # Log successful login
    security_service = SecurityService()
    AuditService.log_action(
        action='staff_login_success',
        performed_by=user,
        details={
            'user_agent': security_service.get_user_agent(request)
        },
        ip_address=security_service.get_client_ip(request)
    )
    
    return Response({
        'success': True,
        'access_token': str(access_token),
        'refresh_token': str(refresh),
        'user': user_serializer.data
    })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def staff_logout(request):
    """
    Staff logout endpoint
    Invalidates the refresh token
    """
    try:
        refresh_token = request.data.get('refresh_token')
        if refresh_token:
            token = RefreshToken(refresh_token)
            token.blacklist()
        
        # Log logout
        security_service = SecurityService()
        AuditService.log_action(
            action='staff_logout',
            performed_by=request.user,
            ip_address=security_service.get_client_ip(request)
        )
        
        return Response({
            'success': True,
            'message': 'Successfully logged out'
        })
    except Exception as e:
        return Response({
            'success': True,
            'message': 'Logged out (token may have been invalid)'
        })


@api_view(['GET'])
@permission_classes([IsStaffUser])
def current_user(request):
    """
    Get current authenticated user information
    """
    serializer = AuthUserSerializer(request.user)
    return Response({
        'success': True,
        'user': serializer.data
    })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def refresh_token(request):
    """
    Refresh access token using refresh token
    """
    try:
        refresh_token = request.data.get('refresh_token')
        if not refresh_token:
            return Response({
                'success': False,
                'error': 'Refresh token is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        token = RefreshToken(refresh_token)
        access_token = token.access_token
        
        return Response({
            'success': True,
            'access_token': str(access_token)
        })
    except Exception as e:
        return Response({
            'success': False,
            'error': 'Invalid refresh token'
        }, status=status.HTTP_401_UNAUTHORIZED)


@api_view(['GET'])
@permission_classes([IsStaffUser])
def user_permissions(request):
    """
    Get current user's permissions and groups
    """
    user = request.user
    
    # Get user groups
    groups = list(user.groups.values_list('name', flat=True))
    
    # Get all permissions
    permissions = list(user.get_all_permissions())
    
    # Get group permissions breakdown
    group_permissions = {}
    for group in user.groups.all():
        group_permissions[group.name] = list(
            group.permissions.values_list('codename', flat=True)
        )
    
    return Response({
        'success': True,
        'user_id': user.id,
        'email': user.email,
        'is_superuser': user.is_superuser,
        'groups': groups,
        'permissions': permissions,
        'group_permissions': group_permissions
    })


@api_view(['POST'])
@permission_classes([IsStaffUser])
def check_permission(request):
    """
    Check if current user has specific permission
    """
    permission = request.data.get('permission')
    
    if not permission:
        return Response({
            'success': False,
            'error': 'Permission parameter is required'
        }, status=status.HTTP_400_BAD_REQUEST)
    
    has_permission = request.user.has_perm(permission)
    
    return Response({
        'success': True,
        'permission': permission,
        'has_permission': has_permission
    })
