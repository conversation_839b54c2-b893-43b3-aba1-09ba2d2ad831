# 01: Simplified RBAC System - Architectural Plan

## **System Overview**

This document outlines a simplified Role-Based Access Control (RBAC) system built on Django's native Groups and Permissions framework, exposed exclusively through Django REST Framework APIs. The system eliminates complex role hierarchies in favor of practical, business-focused group memberships with clear permission boundaries.

---

## **Core Principles**

### **Simplicity First**
- Leverage Django's built-in Groups and Permissions
- No custom role hierarchies or complex inheritance
- Direct group-to-permission mapping
- Clear, business-focused group names

### **API-Only Administration**
- All admin operations via DRF APIs
- React Admin frontend for user interaction
- Django Admin Panel restricted to Superusers only
- Complete API documentation with Postman collections

### **Clear Permission Boundaries**
- Each group has specific, well-defined permissions
- No overlapping or ambiguous access rights
- Business-aligned group structures
- Explicit permission enforcement

---

## **User Types & Access Levels**

### **1. Superuser**
- **Purpose**: System administration and maintenance
- **Access**: Full Django Admin Panel + All API endpoints
- **Responsibilities**: System maintenance, user management, technical operations
- **Restrictions**: Not involved in business operations

### **2. Staff**
- **Purpose**: High-level administrative operations
- **Access**: API-only (no Django Admin Panel)
- **Responsibilities**: Group management, user-to-group assignments, system oversight
- **Permissions**: Create/modify groups, assign users, view system reports

### **3. Group Members**
- **Purpose**: Specific business function execution
- **Access**: API-only with group-specific permissions
- **Responsibilities**: Domain-specific operations based on group membership
- **Permissions**: Limited to group-assigned permissions

---

## **Group Structure & Permissions**

### **Product Management Groups**

#### **Product Management Executive (PME)**
- **Business Role**: Senior product management with full authority
- **Permissions**:
  - Products: Full CRUD (Create, Read, Update, Delete)
  - Categories: Full CRUD + Reordering
  - Brands: Full CRUD
  - Product Types: Full CRUD
  - Attributes: Full CRUD
  - Product Images: Full CRUD
  - Bulk Operations: All product-related bulk actions
  - Reports: Product analytics and export

#### **Product Management Group Member (PMGM)**
- **Business Role**: Day-to-day product management operations
- **Permissions**:
  - Products: Create, Read, Update (no Delete)
  - Categories: Read, Update (no Create/Delete)
  - Brands: Read only
  - Product Types: Read only
  - Attributes: Read, Update
  - Product Images: Create, Read, Update, Delete
  - Bulk Operations: Bulk update only (no bulk delete)
  - Reports: Basic product reports

#### **Product Catalog Viewer (PCV)**
- **Business Role**: Product information access for support/sales
- **Permissions**:
  - Products: Read only
  - Categories: Read only
  - Brands: Read only
  - Product Types: Read only
  - Attributes: Read only
  - Product Images: Read only
  - Reports: View basic product information

### **Order Management Groups**

#### **Order Management Executive (OME)**
- **Business Role**: Senior order management with full authority
- **Permissions**:
  - Orders: Full CRUD + Status changes + Cancellations + Refunds
  - Order Items: Full CRUD
  - Shipping: Full management
  - Payment Status: Update capabilities
  - Customer Orders: Full access
  - Bulk Operations: All order-related bulk actions
  - Reports: Complete order analytics and export

#### **Order Management Group Member (OMGM)**
- **Business Role**: Order processing and status management
- **Permissions**:
  - Orders: Read, Update status (no Delete/Cancel)
  - Order Items: Read, Update
  - Shipping: Update shipping status
  - Payment Status: Read only
  - Customer Orders: Read access
  - Bulk Operations: Bulk status updates only
  - Reports: Basic order reports

#### **Order Fulfillment Specialist (OFS)**
- **Business Role**: Warehouse and shipping operations
- **Permissions**:
  - Orders: Read, Update shipping status only
  - Order Items: Read only
  - Shipping: Full shipping management
  - Payment Status: Read only
  - Customer Orders: Read shipping information only
  - Reports: Shipping and fulfillment reports

### **Customer Management Groups**

#### **Customer Management Executive (CME)**
- **Business Role**: Senior customer relations with full authority
- **Permissions**:
  - Customers: Full CRUD + Account management
  - Customer Addresses: Full CRUD
  - Customer Orders: Full access for support
  - Customer Reviews: Moderate, respond, delete
  - Customer Support: Full ticket management
  - Bulk Operations: All customer-related bulk actions
  - Reports: Complete customer analytics and export

#### **Customer Support Representative (CSR)**
- **Business Role**: Customer service and support operations
- **Permissions**:
  - Customers: Read, Update contact information
  - Customer Addresses: Read, Update
  - Customer Orders: Read for support purposes
  - Customer Reviews: Read, respond (no delete)
  - Customer Support: Create, update support tickets
  - Reports: Basic customer support reports

#### **Customer Data Analyst (CDA)**
- **Business Role**: Customer behavior analysis and reporting
- **Permissions**:
  - Customers: Read only (anonymized data)
  - Customer Orders: Read for analysis
  - Customer Reviews: Read only
  - Reports: Full customer analytics and export
  - Data Export: Customer behavior data

### **Content Management Groups**

#### **Content Management Executive (CTME)**
- **Business Role**: Content strategy and moderation authority
- **Permissions**:
  - Reviews: Full moderation (approve, reject, delete, respond)
  - Content: Full CRUD for site content
  - SEO: Full SEO management
  - Marketing Content: Full CRUD
  - Reports: Content performance analytics

#### **Content Moderator (CTM)**
- **Business Role**: Content review and basic moderation
- **Permissions**:
  - Reviews: Moderate, respond (no delete)
  - Content: Read, Update existing content
  - SEO: Read only
  - Marketing Content: Read, Update
  - Reports: Basic content reports

### **Finance & Analytics Groups**

#### **Finance Manager (FM)**
- **Business Role**: Financial oversight and reporting
- **Permissions**:
  - Orders: Read financial data
  - Payments: Full payment management
  - Refunds: Process refunds
  - Financial Reports: Full access
  - Revenue Analytics: Complete access
  - Data Export: Financial data export

#### **Business Analyst (BA)**
- **Business Role**: Business intelligence and reporting
- **Permissions**:
  - All Data: Read-only access for analysis
  - Reports: Full analytics across all domains
  - Data Export: Comprehensive data export capabilities
  - Dashboards: Create and manage business dashboards

---

## **Technical Architecture**

### **Data Models**

#### **Existing Django Models (Utilized)**
- **User**: Django's built-in User model (extended via Customer)
- **Group**: Django's built-in Group model
- **Permission**: Django's built-in Permission model
- **Customer**: Existing customer model linked to User

#### **New Models (Minimal)**
- **GroupMembership**: Track group assignments with metadata
- **PermissionAudit**: Log permission-related actions
- **APIAccessLog**: Track API usage for monitoring

### **API Structure**

#### **Authentication Endpoints**
- Login/Logout with JWT tokens
- Token refresh and validation
- User profile and permissions retrieval

#### **Group Management Endpoints (Staff Only)**
- Group CRUD operations
- User-to-group assignment/removal
- Permission-to-group assignment
- Group membership listing

#### **Business Domain Endpoints**
- Product Management APIs
- Order Management APIs
- Customer Management APIs
- Content Management APIs
- Analytics and Reporting APIs

#### **System Administration Endpoints (Superuser Only)**
- User management
- System health and monitoring
- Audit log access
- Permission system management

### **Permission Enforcement Strategy**

#### **API Level**
- Custom DRF permission classes for each group
- Decorator-based permission checking
- Automatic permission validation on all endpoints

#### **View Level**
- Group-based access control in viewsets
- Method-level permission granularity
- Dynamic permission checking based on user groups

#### **Object Level**
- Resource-specific access control where needed
- Owner-based permissions for user-generated content
- Contextual permission checking

### **Security Features**

#### **Authentication Security**
- JWT token-based authentication
- Token expiration and refresh mechanism
- Secure token storage and transmission

#### **Authorization Security**
- Explicit permission checking on all operations
- Group membership validation
- Action-based permission enforcement

#### **Audit & Monitoring**
- Comprehensive API access logging
- Permission change tracking
- Security event monitoring
- Failed access attempt logging

---

## **Integration Points**

### **Existing System Integration**
- Seamless integration with current User/Customer models
- Backward compatibility with existing permission checks
- Gradual migration from current admin system
- Preservation of existing business logic

### **Frontend Integration**
- React Admin interface consuming DRF APIs
- Role-based UI component rendering
- Dynamic menu generation based on permissions
- Real-time permission validation

### **External System Integration**
- API endpoints for third-party integrations
- Webhook support for permission changes
- Export capabilities for external reporting tools
- Integration-friendly data formats

---

## **Implementation Benefits**

### **Simplicity**
- Easy to understand and maintain
- Clear permission boundaries
- Minimal learning curve for developers
- Straightforward troubleshooting

### **Scalability**
- Built on Django's proven Group system
- Efficient permission checking
- Easy to add new groups and permissions
- Horizontal scaling friendly

### **Security**
- Explicit permission enforcement
- Comprehensive audit trails
- Secure API-only access
- Clear separation of concerns

### **Maintainability**
- Standard Django patterns
- Well-documented API endpoints
- Clear group-permission mappings
- Easy permission updates

---

## **Success Criteria**

### **Functional Requirements**
- All admin operations accessible via API
- Clear group-based permission enforcement
- Comprehensive user-to-group management
- Complete audit trail for all actions

### **Technical Requirements**
- Sub-100ms API response times
- 99.9% API availability
- Comprehensive API documentation
- Full Postman collection coverage

### **Security Requirements**
- Zero unauthorized access incidents
- Complete action auditability
- Secure token management
- Proper permission isolation

### **Usability Requirements**
- Intuitive React Admin interface
- Clear error messages and feedback
- Efficient group management workflows
- Easy permission troubleshooting

This simplified RBAC system provides a practical, maintainable solution that meets business needs without unnecessary complexity while ensuring security and scalability.
