# 04: RBAC API Collection

## **Base URL**
```
http://localhost:8000/api/staff/
```

## **Authentication Endpoints**

### **1. Staff Login**
```http
POST /api/staff/auth/login/
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "your_password"
}
```

**Success Response (200):**
```json
{
    "success": true,
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "user": {
        "id": 1,
        "email": "<EMAIL>",
        "is_staff": true,
        "is_superuser": false,
        "full_name": "Admin User",
        "groups": ["Product Management Executive (PME)"],
        "permissions": ["products.add_product", "products.change_product", ...]
    }
}
```

**Error Response (403):**
```json
{
    "success": false,
    "error": "Admin access required"
}
```

### **2. Admin <PERSON>gout**
```http
POST /api/admin/auth/logout/
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "Successfully logged out"
}
```

### **3. Get Current User**
```http
GET /api/admin/auth/user/
Authorization: Bearer {access_token}
```

**Success Response (200):**
```json
{
    "success": true,
    "user": {
        "id": 1,
        "email": "<EMAIL>",
        "is_staff": true,
        "is_superuser": false,
        "full_name": "Admin User",
        "groups": ["Product Management Executive (PME)"],
        "permissions": ["products.add_product", "products.change_product", ...]
    }
}
```

### **4. Refresh Token**
```http
POST /api/admin/auth/refresh/
Content-Type: application/json

{
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**Success Response (200):**
```json
{
    "success": true,
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

---

## **Group Management Endpoints**

### **5. List All Groups**
```http
GET /api/admin/groups/
Authorization: Bearer {access_token}
```

**Success Response (200):**
```json
[
    {
        "id": 1,
        "name": "Product Management Executive (PME)",
        "permissions": [
            {
                "id": 25,
                "name": "Can add product",
                "codename": "add_product",
                "content_type": 8
            }
        ],
        "member_count": 3
    }
]
```

### **6. Create New Group**
```http
POST /api/admin/groups/
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "name": "New Admin Group",
    "permission_ids": [25, 26, 27, 28]
}
```

**Success Response (201):**
```json
{
    "id": 15,
    "name": "New Admin Group",
    "permissions": [
        {
            "id": 25,
            "name": "Can add product",
            "codename": "add_product",
            "content_type": 8
        }
    ],
    "member_count": 0
}
```

### **7. Update Group**
```http
PUT /api/admin/groups/{group_id}/
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "name": "Updated Group Name",
    "permission_ids": [25, 26, 27]
}
```

**Success Response (200):**
```json
{
    "id": 15,
    "name": "Updated Group Name",
    "permissions": [...],
    "member_count": 0
}
```

### **8. Delete Group (Superuser Only)**
```http
DELETE /api/admin/groups/{group_id}/
Authorization: Bearer {access_token}
```

**Success Response (204):**
```
No Content
```

**Error Response (403):**
```json
{
    "success": false,
    "error": "Only superusers can delete groups"
}
```

### **9. Get Group Members**
```http
GET /api/admin/groups/{group_id}/members/
Authorization: Bearer {access_token}
```

**Success Response (200):**
```json
{
    "success": true,
    "members": [
        {
            "id": 1,
            "user": {
                "id": 5,
                "email": "<EMAIL>",
                "is_active": true,
                "is_staff": true,
                "is_superuser": false,
                "full_name": "John Doe",
                "date_joined": "2024-01-15T10:30:00Z"
            },
            "group": {
                "id": 1,
                "name": "Product Management Executive (PME)",
                "permissions": [...],
                "member_count": 3
            },
            "assigned_by_email": "<EMAIL>",
            "assigned_at": "2024-01-15T10:30:00Z",
            "is_active": true,
            "notes": "Initial assignment"
        }
    ]
}
```

### **10. Add User to Group**
```http
POST /api/admin/groups/{group_id}/add_member/
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "user_id": 5,
    "notes": "Adding user to product management team"
}
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "User <EMAIL> added to group Product Management Executive (PME)",
    "membership": {
        "id": 10,
        "user": {...},
        "group": {...},
        "assigned_by_email": "<EMAIL>",
        "assigned_at": "2024-01-15T10:30:00Z",
        "is_active": true,
        "notes": "Adding user to product management team"
    }
}
```

**Error Response (400):**
```json
{
    "success": false,
    "error": "User is already a member of this group"
}
```

### **11. Remove User from Group**
```http
POST /api/admin/groups/{group_id}/remove_member/
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "user_id": 5
}
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "User <EMAIL> removed from group Product Management Executive (PME)"
}
```

---

## **User Management Endpoints**

### **12. List Users**
```http
GET /api/admin/users/
Authorization: Bearer {access_token}
```

**Success Response (200):**
```json
[
    {
        "id": 1,
        "email": "<EMAIL>",
        "is_active": true,
        "is_staff": true,
        "is_superuser": false,
        "full_name": "Admin User",
        "date_joined": "2024-01-01T00:00:00Z"
    }
]
```

### **13. Get User Details**
```http
GET /api/admin/users/{user_id}/
Authorization: Bearer {access_token}
```

**Success Response (200):**
```json
{
    "id": 1,
    "email": "<EMAIL>",
    "is_active": true,
    "is_staff": true,
    "is_superuser": false,
    "full_name": "Admin User",
    "date_joined": "2024-01-01T00:00:00Z",
    "last_login": "2024-01-15T10:30:00Z",
    "groups": [
        {
            "id": 1,
            "name": "Product Management Executive (PME)",
            "permissions": [...],
            "member_count": 3
        }
    ],
    "user_permissions": [],
    "all_permissions": ["products.add_product", "products.change_product", ...]
}
```

### **14. Get User Groups**
```http
GET /api/admin/users/{user_id}/groups/
Authorization: Bearer {access_token}
```

**Success Response (200):**
```json
{
    "success": true,
    "groups": [
        {
            "id": 1,
            "user": {...},
            "group": {...},
            "assigned_by_email": "<EMAIL>",
            "assigned_at": "2024-01-15T10:30:00Z",
            "is_active": true,
            "notes": "Initial assignment"
        }
    ]
}
```

### **15. Toggle User Staff Status (Superuser Only)**
```http
POST /api/admin/users/{user_id}/toggle_staff/
Authorization: Bearer {access_token}
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "User <EMAIL> staff status set to true",
    "is_staff": true
}
```

---

## **Permission Management Endpoints**

### **16. List All Permissions**
```http
GET /api/admin/permissions/
Authorization: Bearer {access_token}
```

**Success Response (200):**
```json
{
    "success": true,
    "permissions": {
        "products.product": {
            "app_label": "products",
            "model": "product",
            "permissions": [
                {
                    "id": 25,
                    "name": "Can add product",
                    "codename": "add_product"
                },
                {
                    "id": 26,
                    "name": "Can change product",
                    "codename": "change_product"
                }
            ]
        }
    }
}
```

---

## **Audit & Monitoring Endpoints**

### **17. List Audit Logs**
```http
GET /api/admin/audit/
Authorization: Bearer {access_token}
```

**Query Parameters:**
- `action`: Filter by action type
- `user_id`: Filter by user ID
- `from_date`: Filter from date (YYYY-MM-DD)
- `to_date`: Filter to date (YYYY-MM-DD)

**Success Response (200):**
```json
[
    {
        "id": 1,
        "action": "user_added_to_group",
        "performed_by_email": "<EMAIL>",
        "target_user_email": "<EMAIL>",
        "target_group_name": "Product Management Executive (PME)",
        "details": {
            "notes": "Initial assignment"
        },
        "ip_address": "*************",
        "timestamp": "2024-01-15T10:30:00Z"
    }
]
```

### **18. Get Audit Summary**
```http
GET /api/admin/audit/summary/
Authorization: Bearer {access_token}
```

**Success Response (200):**
```json
{
    "success": true,
    "summary": {
        "total_actions_30_days": 45,
        "action_breakdown": [
            {
                "action": "user_added_to_group",
                "count": 15
            },
            {
                "action": "group_created",
                "count": 8
            }
        ],
        "top_users": [
            {
                "performed_by__email": "<EMAIL>",
                "count": 25
            }
        ]
    }
}
```

---

## **Error Responses**

### **Authentication Errors**
```json
{
    "detail": "Given token not valid for any token type",
    "code": "token_not_valid",
    "messages": [
        {
            "token_class": "AccessToken",
            "token_type": "access",
            "message": "Token is invalid or expired"
        }
    ]
}
```

### **Permission Errors**
```json
{
    "detail": "You do not have permission to perform this action."
}
```

### **Validation Errors**
```json
{
    "success": false,
    "errors": {
        "user_id": ["This field is required."],
        "group_id": ["Group not found"]
    }
}
```

---

## **Postman Environment Variables**

```json
{
    "base_url": "http://localhost:8000/api/admin",
    "access_token": "{{access_token}}",
    "refresh_token": "{{refresh_token}}",
    "user_id": "{{user_id}}",
    "group_id": "{{group_id}}"
}
```

## **Postman Pre-request Script (Global)**

```javascript
// Auto-refresh token if expired
const accessToken = pm.environment.get("access_token");
if (accessToken) {
    // Check if token is expired (basic check)
    const tokenData = JSON.parse(atob(accessToken.split('.')[1]));
    const currentTime = Math.floor(Date.now() / 1000);
    
    if (tokenData.exp < currentTime) {
        console.log("Token expired, refreshing...");
        // Refresh token logic would go here
    }
}
```

## **Postman Test Script (Global)**

```javascript
// Test for successful responses
pm.test("Status code is successful", function () {
    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);
});

// Test response time
pm.test("Response time is less than 1000ms", function () {
    pm.expect(pm.response.responseTime).to.be.below(1000);
});

// Save tokens from login response
if (pm.response.json() && pm.response.json().access_token) {
    pm.environment.set("access_token", pm.response.json().access_token);
    pm.environment.set("refresh_token", pm.response.json().refresh_token);
}
```

This comprehensive API collection provides all the necessary endpoints for testing and integrating with the RBAC system. Each endpoint includes detailed request/response examples and proper error handling.
