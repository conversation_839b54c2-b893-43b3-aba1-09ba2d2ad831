# 04: RBAC API Collection

## **Base URLs**
```
Authentication: http://localhost:8000/api/auth/
Staff Operations: http://localhost:8000/api/staff/
```

## **Authentication Endpoints (Core App)**

### **1. Staff Login (Use Existing Core Endpoint)**
```http
POST /api/auth/login/
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "your_password"
}
```

**Success Response (200):**
```json
{
    "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "user": {
        "id": 1,
        "email": "<EMAIL>",
        "is_staff": true,
        "is_superuser": false
    }
}
```

**Error Response (401):**
```json
{
    "detail": "No active account found with the given credentials"
}
```

### **2. Staff Logout (Use Existing Core Endpoint)**
```http
POST /api/auth/logout/
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**Success Response (200):**
```json
{
    "detail": "Successfully logged out"
}
```

### **3. Refresh Token (Use Existing Core Endpoint)**
```http
POST /api/auth/token/refresh/
Content-Type: application/json

{
    "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**Success Response (200):**
```json
{
    "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

## **Staff Authorization Endpoints**

### **4. Get Staff User Info**
```http
GET /api/staff/auth/user/
Authorization: Bearer {access_token}
```

**Success Response (200):**
```json
{
    "success": true,
    "user": {
        "id": 1,
        "email": "<EMAIL>",
        "is_staff": true,
        "is_superuser": false,
        "full_name": "Staff User",
        "groups": ["Product Management Executive (PME)"],
        "permissions": ["products.add_product", "products.change_product", ...]
    }
}
```

**Error Response (403):**
```json
{
    "detail": "You do not have permission to perform this action."
}
```

### **5. Check User Permissions**
```http
GET /api/staff/auth/permissions/
Authorization: Bearer {access_token}
```

**Success Response (200):**
```json
{
    "success": true,
    "user_id": 1,
    "email": "<EMAIL>",
    "is_superuser": false,
    "groups": ["Product Management Executive (PME)"],
    "permissions": ["products.add_product", "products.change_product", ...],
    "group_permissions": {
        "Product Management Executive (PME)": ["add_product", "change_product", ...]
    }
}
```

### **6. Check Specific Permission**
```http
POST /api/staff/auth/check-permission/
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "permission": "products.add_product"
}
```

**Success Response (200):**
```json
{
    "success": true,
    "permission": "products.add_product",
    "has_permission": true
}
```

---

## **Group Management Endpoints**

## **Group Management Endpoints**

### **7. List All Groups**
```http
GET /api/staff/groups/
Authorization: Bearer {access_token}
```

**Success Response (200):**
```json
[
    {
        "id": 1,
        "name": "Product Management Executive (PME)",
        "permissions": [
            {
                "id": 25,
                "name": "Can add product",
                "codename": "add_product",
                "content_type": 8
            }
        ],
        "member_count": 3
    }
]
```

### **8. Create New Group**
```http
POST /api/staff/groups/
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "name": "New Staff Group",
    "permission_ids": [25, 26, 27, 28]
}
```

**Success Response (201):**
```json
{
    "id": 15,
    "name": "New Staff Group",
    "permissions": [
        {
            "id": 25,
            "name": "Can add product",
            "codename": "add_product",
            "content_type": 8
        }
    ],
    "member_count": 0
}
```

### **9. Update Group**
```http
PUT /api/staff/groups/{group_id}/
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "name": "Updated Group Name",
    "permission_ids": [25, 26, 27]
}
```

**Success Response (200):**
```json
{
    "id": 15,
    "name": "Updated Group Name",
    "permissions": [...],
    "member_count": 0
}
```

### **10. Delete Group (Superuser Only)**
```http
DELETE /api/staff/groups/{group_id}/
Authorization: Bearer {access_token}
```

**Success Response (204):**
```
No Content
```

**Error Response (403):**
```json
{
    "success": false,
    "error": "Only superusers can delete groups"
}
```

### **11. Get Group Members**
```http
GET /api/staff/groups/{group_id}/members/
Authorization: Bearer {access_token}
```

**Success Response (200):**
```json
{
    "success": true,
    "members": [
        {
            "id": 1,
            "user": {
                "id": 5,
                "email": "<EMAIL>",
                "is_active": true,
                "is_staff": true,
                "is_superuser": false,
                "full_name": "John Doe",
                "date_joined": "2024-01-15T10:30:00Z"
            },
            "group": {
                "id": 1,
                "name": "Product Management Executive (PME)",
                "permissions": [...],
                "member_count": 3
            },
            "assigned_by_email": "<EMAIL>",
            "assigned_at": "2024-01-15T10:30:00Z",
            "is_active": true,
            "notes": "Initial assignment"
        }
    ]
}
```

### **12. Add User to Group**
```http
POST /api/staff/groups/{group_id}/add_member/
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "user_id": 5,
    "notes": "Adding user to product management team"
}
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "User <EMAIL> added to group Product Management Executive (PME)",
    "membership": {
        "id": 10,
        "user": {...},
        "group": {...},
        "assigned_by_email": "<EMAIL>",
        "assigned_at": "2024-01-15T10:30:00Z",
        "is_active": true,
        "notes": "Adding user to product management team"
    }
}
```

**Error Response (400):**
```json
{
    "success": false,
    "error": "User is already a member of this group"
}
```

### **13. Remove User from Group**
```http
POST /api/staff/groups/{group_id}/remove_member/
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "user_id": 5
}
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "User <EMAIL> removed from group Product Management Executive (PME)"
}
```

---

## **User Management Endpoints**

## **User Management Endpoints**

### **14. List Users**
```http
GET /api/staff/users/
Authorization: Bearer {access_token}
```

**Success Response (200):**
```json
[
    {
        "id": 1,
        "email": "<EMAIL>",
        "is_active": true,
        "is_staff": true,
        "is_superuser": false,
        "full_name": "Admin User",
        "date_joined": "2024-01-01T00:00:00Z"
    }
]
```

### **15. Get User Details**
```http
GET /api/staff/users/{user_id}/
Authorization: Bearer {access_token}
```

**Success Response (200):**
```json
{
    "id": 1,
    "email": "<EMAIL>",
    "is_active": true,
    "is_staff": true,
    "is_superuser": false,
    "full_name": "Admin User",
    "date_joined": "2024-01-01T00:00:00Z",
    "last_login": "2024-01-15T10:30:00Z",
    "groups": [
        {
            "id": 1,
            "name": "Product Management Executive (PME)",
            "permissions": [...],
            "member_count": 3
        }
    ],
    "user_permissions": [],
    "all_permissions": ["products.add_product", "products.change_product", ...]
}
```

### **16. Get User Groups**
```http
GET /api/staff/users/{user_id}/groups/
Authorization: Bearer {access_token}
```

**Success Response (200):**
```json
{
    "success": true,
    "groups": [
        {
            "id": 1,
            "user": {...},
            "group": {...},
            "assigned_by_email": "<EMAIL>",
            "assigned_at": "2024-01-15T10:30:00Z",
            "is_active": true,
            "notes": "Initial assignment"
        }
    ]
}
```

### **17. Toggle User Staff Status (Superuser Only)**
```http
POST /api/staff/users/{user_id}/toggle_staff/
Authorization: Bearer {access_token}
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "User <EMAIL> staff status set to true",
    "is_staff": true
}
```

---

## **Permission Management Endpoints**

## **Permission Management Endpoints**

### **18. List All Permissions**
```http
GET /api/staff/permissions/
Authorization: Bearer {access_token}
```

**Success Response (200):**
```json
{
    "success": true,
    "permissions": {
        "products.product": {
            "app_label": "products",
            "model": "product",
            "permissions": [
                {
                    "id": 25,
                    "name": "Can add product",
                    "codename": "add_product"
                },
                {
                    "id": 26,
                    "name": "Can change product",
                    "codename": "change_product"
                }
            ]
        }
    }
}
```

---

## **Audit & Monitoring Endpoints**

## **Audit & Monitoring Endpoints**

### **19. List Audit Logs**
```http
GET /api/staff/audit/
Authorization: Bearer {access_token}
```

**Query Parameters:**
- `action`: Filter by action type
- `user_id`: Filter by user ID
- `from_date`: Filter from date (YYYY-MM-DD)
- `to_date`: Filter to date (YYYY-MM-DD)

**Success Response (200):**
```json
[
    {
        "id": 1,
        "action": "user_added_to_group",
        "performed_by_email": "<EMAIL>",
        "target_user_email": "<EMAIL>",
        "target_group_name": "Product Management Executive (PME)",
        "details": {
            "notes": "Initial assignment"
        },
        "ip_address": "*************",
        "timestamp": "2024-01-15T10:30:00Z"
    }
]
```

### **20. Get Audit Summary**
```http
GET /api/staff/audit/summary/
Authorization: Bearer {access_token}
```

**Success Response (200):**
```json
{
    "success": true,
    "summary": {
        "total_actions_30_days": 45,
        "action_breakdown": [
            {
                "action": "user_added_to_group",
                "count": 15
            },
            {
                "action": "group_created",
                "count": 8
            }
        ],
        "top_users": [
            {
                "performed_by__email": "<EMAIL>",
                "count": 25
            }
        ]
    }
}
```

---

## **Error Responses**

### **Authentication Errors**
```json
{
    "detail": "Given token not valid for any token type",
    "code": "token_not_valid",
    "messages": [
        {
            "token_class": "AccessToken",
            "token_type": "access",
            "message": "Token is invalid or expired"
        }
    ]
}
```

### **Permission Errors**
```json
{
    "detail": "You do not have permission to perform this action."
}
```

### **Validation Errors**
```json
{
    "success": false,
    "errors": {
        "user_id": ["This field is required."],
        "group_id": ["Group not found"]
    }
}
```

---

## **Postman Environment Variables**

```json
{
    "auth_base_url": "http://localhost:8000/api/auth",
    "staff_base_url": "http://localhost:8000/api/staff",
    "access_token": "{{access_token}}",
    "refresh_token": "{{refresh_token}}",
    "user_id": "{{user_id}}",
    "group_id": "{{group_id}}"
}
```

## **Postman Pre-request Script (Global)**

```javascript
// Auto-refresh token if expired
const accessToken = pm.environment.get("access_token");
if (accessToken) {
    // Check if token is expired (basic check)
    const tokenData = JSON.parse(atob(accessToken.split('.')[1]));
    const currentTime = Math.floor(Date.now() / 1000);
    
    if (tokenData.exp < currentTime) {
        console.log("Token expired, refreshing...");
        // Refresh token logic would go here
    }
}
```

## **Postman Test Script (Global)**

```javascript
// Test for successful responses
pm.test("Status code is successful", function () {
    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);
});

// Test response time
pm.test("Response time is less than 1000ms", function () {
    pm.expect(pm.response.responseTime).to.be.below(1000);
});

// Save tokens from login response
if (pm.response.json() && pm.response.json().access_token) {
    pm.environment.set("access_token", pm.response.json().access_token);
    pm.environment.set("refresh_token", pm.response.json().refresh_token);
}
```

This comprehensive API collection provides all the necessary endpoints for testing and integrating with the RBAC system. Each endpoint includes detailed request/response examples and proper error handling.
